import { set<PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON>, delete<PERSON><PERSON><PERSON> } from "cookies-next";
import { JwtPayloadX, TokenPair } from "@/types/auth.types";
import { jwtDecode } from "jwt-decode";
import {
  ACCESS_COOKIE,
  API_ROUTES,
  REFRESH_COOKIE,
} from "@/constants/api.consants";
import {
  ACCESS_TOKEN_EXPIRY,
  REFRESH_TOKEN_EXPIRY,
} from "@/constants/auth.constants";

const FIVE_MIN = 5 * 60; // pre-refresh window (s)
let refreshPromise: Promise<string> | null = null; // dedupe parallel calls

export async function signup(email: string, password: string, firstName = "") {
  const res = await fetch(`${API_ROUTES.AUTH.REGISTER}`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email, password, first_name: firstName }),
  });
  if (!res.ok) {
    const data = await res.json().catch(() => ({}));
    throw new Error(
      data?.email?.[0] ??
        data?.password?.[0] ??
        data?.first_name?.[0] ??
        "Unable to create account"
    );
  }
}

export async function login(email: string, password: string) {
  const res = await fetch(API_ROUTES.AUTH.LOGIN, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email, password }),
  });
  if (!res.ok) throw new Error("Bad credentials");
  const data: TokenPair = await res.json();
  storeTokens(data);
}

export async function logout() {
  const refresh = getCookie(REFRESH_COOKIE) as string | undefined;
  if (refresh) {
    fetch(API_ROUTES.AUTH.LOGOUT, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ refresh }),
    }).catch(() => {});
  }
  clearTokens();
}

export async function getValidAccessToken(): Promise<string | null> {
  const access = getCookie(ACCESS_COOKIE) as string | undefined;
  if (!access) return null;

  const { exp } = jwtDecode<JwtPayloadX>(access);
  const now = Date.now() / 1000;

  if (!exp || typeof exp !== "number") {
    clearTokens();
    throw new Error("Invalid access token");
  }
  // still fresh?
  if (exp - now > FIVE_MIN) return access;

  // one refresh in flight at a time:
  refreshPromise ??= refreshAccessToken();
  try {
    const newAccess = await refreshPromise;
    return newAccess;
  } finally {
    refreshPromise = null;
  }
}

async function refreshAccessToken(): Promise<string> {
  const refresh = getCookie(REFRESH_COOKIE) as string | undefined;
  if (!refresh) throw new Error("No refresh token");

  const res = await fetch(API_ROUTES.AUTH.REFRESH, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ refresh }),
  });
  if (!res.ok) {
    clearTokens();
    throw new Error("Refresh failed");
  }
  const data: TokenPair = await res.json();
  storeTokens(data);
  return data.access;
}

function storeTokens({ access, refresh }: TokenPair) {
  setCookie(ACCESS_COOKIE, access, {
    maxAge: Number(ACCESS_TOKEN_EXPIRY),
    sameSite: "lax",
    secure: true,
  });
  setCookie(REFRESH_COOKIE, refresh, {
    maxAge: Number(REFRESH_TOKEN_EXPIRY),
    sameSite: "lax",
    secure: true,
  });
}

function clearTokens() {
  deleteCookie(ACCESS_COOKIE);
  deleteCookie(REFRESH_COOKIE);
}
