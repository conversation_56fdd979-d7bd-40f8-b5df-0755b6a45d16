"use client";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ArrowLeft, Eye, EyeOff } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { getValidAccessToken, login } from "@/services/auth";
import ThemedButton from "@/components/common/ThemedButton";
import toast from "react-hot-toast"; // or any toaster lib
import { useAuth } from "@/providers/AuthProvider";
import { jwtDecode } from "jwt-decode";
import SocialButton from "@/components/common/SocialButton";

const schema = z.object({
  email: z.string().email({ message: "Invalid e-mail" }),
  password: z.string().min(8, "Min 8 characters"),
});
type FormData = z.infer<typeof schema>;

export default function SignIn() {
  const router = useRouter();
  const { updateUser } = useAuth();
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  async function onSubmit(data: FormData) {
    try {
      await login(data.email, data.password);
      const token = await getValidAccessToken();
      updateUser(token ? jwtDecode(token) : null);
      toast.success("Welcome back!");
      router.replace("/");
    } catch (err) {
      const msg =
        err instanceof Error ? err.message : "Login failed. Try again.";
      toast.error(msg);
    }
  }

  return (
    <div className="grid min-h-screen grid-cols-1 lg:grid-cols-2">
      <aside className="hidden flex-col bg-theme_primary lg:flex">
        <div className="px-8 py-8 text-white">
          <h2 className="font-plus-jakarta-sans font-semibold  lg:text-3xl xl:text-4xl">
            Rahnaward
            <br />
            Academy
          </h2>
        </div>

        <div className="relative flex flex-1 items-center justify-center overflow-hidden">
          <Image
            src="/vectors/vector_hero.png"
            alt=""
            fill
            priority
            className="object-contain"
          />

          <Image
            src="/assets/auth/signin_hero.png"
            alt="Students high-fiving"
            fill
            priority
            className="relative object-contain shadow-xl p-[11dvh]"
          />
        </div>
      </aside>

      <main className="flex flex-col w-full bg-neutral-100 px-6 py-10 sm:px-12 xl:px-20">
        <Link
          href="/"
          className="font-inter font-semibold mb-6 flex items-center gap-2 text-sm text-[#1B263B]"
        >
          <ArrowLeft size={18} />
          Back
        </Link>

        <div className="flex flex-col w-full h-full items-center justify-center px-[7dvw]">
          <h1 className="text-center font-plus-jakarta-sans text-3xl font-semibold text-theme_primary">
            Welcome
          </h1>
          <p className="mb-10 mt-2 text-center font-inter font-semibold text-sm text-[#1B263B]">
            Login to Rahnaward Academy
          </p>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-4 w-full"
          >
            <div>
              <input
                {...register("email")}
                type="email"
                placeholder="Enter Email Address*"
                autoComplete="email"
                className={`w-full rounded-md border bg-white p-3 text-sm font-inter outline-none ${
                  errors.email
                    ? "border-red-500 focus:border-red-500"
                    : "border-[#A5A9B2] focus:border-theme_primary"
                } text-[#1B263B]`}
              />
              {errors.email && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div className="relative">
              <input
                {...register("password")}
                type={showPassword ? "text" : "password"}
                autoComplete="current-password"
                placeholder="Enter Password"
                className={`w-full rounded-md border bg-white p-3 pr-12 text-sm font-inter outline-none ${
                  errors.password
                    ? "border-red-500 focus:border-red-500"
                    : "border-[#A5A9B2] focus:border-theme_primary"
                } text-[#1B263B]`}
              />
              <button
                type="button"
                onClick={() => setShowPassword((v) => !v)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
              {errors.password && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.password.message}
                </p>
              )}
            </div>

            <div className="text-left">
              <Link
                href="#"
                className="text-sm text-theme_primary font-semibold font-inter"
              >
                Forgot Password?
              </Link>
            </div>

            <ThemedButton
              type="submit"
              disabled={isSubmitting}
              addClassName="w-full rounded-md !text-base !font-semibold disabled:opacity-60"
            >
              {isSubmitting ? "Signing in…" : "Sign In"}
            </ThemedButton>
          </form>

          <p className="mt-4 text-center text-sm text-[#1B263B]">
            <span className="font-inter">Don’t have an account?</span>

            <Link
              href="/signup"
              className="ml-5 font-plus-jakarta-sans font-semibold text-[#091B33] text-lg"
            >
              Sign Up
            </Link>
          </p>

          <div className="my-8 flex items-center gap-4 w-full">
            <div className="h-px bg-[#1B263B]/20  w-full" />
            <span className="font-inter">or</span>
            <div className="h-px bg-[#1B263B]/20  w-full" />
          </div>

          <div className="space-y-4 w-full">
            <SocialButton
              provider="Google"
              iconSrc="/icons/google_icon.png"
            />
            <SocialButton
              provider="Apple"
              iconSrc="/icons/apple_icon.webp"
            />
            <SocialButton
              provider="Facebook"
              iconSrc="/icons/facebook_icon.jpg"
            />
          </div>
        </div>
      </main>
    </div>
  );
}
