"use client";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ArrowL<PERSON>t, Eye, EyeOff } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import toast from "react-hot-toast";
import { jwtDecode } from "jwt-decode";

import { signup, login, getValidAccessToken } from "@/services/auth";
import { useAuth } from "@/providers/AuthProvider";
import ThemedButton from "@/components/common/ThemedButton";
import { JwtPayloadX } from "@/types/auth.types";
import SocialButton from "@/components/common/SocialButton";
import { ROUTES } from "@/constants/routes.constants";

const schema = z.object({
  email: z.string().email({ message: "Invalid e-mail" }),
  password: z.string().min(8, "Password must be at least 8 characters"),
});
type FormData = z.infer<typeof schema>;

export default function SignUp() {
  const router = useRouter();
  const { updateUser } = useAuth();

  const [showPw, setShowPw] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  const onSubmit = async (data: FormData) => {
    try {
      await signup(data.email, data.password);
      await login(data.email, data.password);
      const token = await getValidAccessToken();

      updateUser(token ? (jwtDecode(token) as JwtPayloadX) : null);

      toast.success("Account created! Welcome 🎉");
      router.replace("/"); // 5️⃣ go home
    } catch (err) {
      toast.error(
        err instanceof Error ? err.message : "Sign-up failed. Try again."
      );
    }
  };

  return (
    <div className="grid min-h-screen grid-cols-1 lg:grid-cols-2">
      <aside className="hidden flex-col bg-theme_primary lg:flex">
        <div className="px-8 py-8 text-white">
          <h2 className="font-plus-jakarta-sans font-semibold lg:text-3xl xl:text-4xl">
            Rahnaward
            <br />
            Academy
          </h2>
        </div>

        <div className="relative flex flex-1 items-center justify-center overflow-hidden">
          <Image
            src="/vectors/vector_hero.png"
            alt=""
            fill
            priority
            className="object-contain"
          />
          <Image
            src="/assets/auth/signup_hero.png"
            alt="Students high-fiving"
            fill
            priority
            className="relative object-contain shadow-xl p-[11dvh]"
          />
        </div>
      </aside>

      <main className="flex flex-col w-full bg-neutral-100 px-6 py-10 sm:px-12 xl:px-20">
        <Link
          href="/"
          className="mb-6 flex items-center gap-2 text-sm font-inter"
        >
          <ArrowLeft size={18} /> Back
        </Link>

        <div className="flex grow flex-col items-center justify-center px-[7dvw]">
          <h2 className="text-center font-plus-jakarta-sans text-xl font-semibold text-[#0D1B2A]">
            Build resilience, think strategically,
            <br />
            speak powerfully all in one place.
          </h2>
          <p className="mt-2 mb-10 text-center  font-inter text-[#1B263B] font-semibold">
            Join for free. Learn for life.
          </p>

          <SocialBlock />

          <div className="my-6 flex w-full items-center gap-4">
            <div className="h-px w-full bg-[#1B263B]/20" />
            <span className="text-sm font-inter">or</span>
            <div className="h-px w-full bg-[#1B263B]/20" />
          </div>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-4 w-full"
          >
            <div>
              <input
                {...register("email")}
                type="email"
                autoComplete="email"
                placeholder="Email Address*"
                className={`font-inter w-full rounded-md border bg-white p-3 text-sm outline-none text-[#1B263B] ${
                  errors.email
                    ? "border-red-500 focus:border-red-500"
                    : "border-[#A5A9B2] focus:border-theme_primary"
                }`}
              />
              {errors.email && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div className="relative">
              <input
                {...register("password")}
                type={showPw ? "text" : "password"}
                autoComplete="new-password"
                placeholder="Create Password*"
                className={`font-inter w-full rounded-md border bg-white p-3 pr-12 text-sm outline-none text-[#1B263B] ${
                  errors.password
                    ? "border-red-500 focus:border-red-500"
                    : "border-[#A5A9B2] focus:border-theme_primary"
                }`}
              />
              <button
                type="button"
                onClick={() => setShowPw((v) => !v)}
                className="absolute right-3 top-5 -translate-y-1/2 text-neutral-500"
              >
                {showPw ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
              {errors.password ? (
                <p className="mt-1 text-xs text-red-600">
                  {errors.password.message}
                </p>
              ) : (
                <p className="font-inter mt-1 text-sm text-[#1B263B]">
                  Password must be at least 8 characters long.
                </p>
              )}
            </div>

            <ThemedButton
              type="submit"
              disabled={isSubmitting}
              addClassName="w-full rounded-md !text-base font-semibold disabled:opacity-60"
            >
              {isSubmitting ? "Creating account…" : "Sign Up"}
            </ThemedButton>
          </form>

          <p className="my-6 font-inter text-sm text-[#1B263B]">
            Already a member?{" "}
            <Link
              href={ROUTES.SIGN_IN}
              className="ml-5 font-plus-jakarta-sans text-lg font-semibold cursor-pointer text-[#1B263B] hover:underline"
            >
              Sign in
            </Link>
          </p>

          <div className="h-px w-full bg-[#1B263B]/20" />

          <p className="font-inter mt-8 text-center text-sm text-[#1B263B]">
            By signing up you agree to Rahnaward Academy’s{" "}
            <span className=" font-semibold">Terms of Services</span> and{" "}
            <span className="font-semibold">Privacy Policy</span>.
          </p>
        </div>
      </main>
    </div>
  );
}

function SocialBlock() {
  return (
    <div className="space-y-4 w-full">
      <SocialButton
        provider="Google"
        iconSrc="/icons/google_icon.png"
      />
      <SocialButton
        provider="Apple"
        iconSrc="/icons/apple_icon.webp"
      />
      <SocialButton
        provider="Facebook"
        iconSrc="/icons/facebook_icon.jpg"
      />
    </div>
  );
}
