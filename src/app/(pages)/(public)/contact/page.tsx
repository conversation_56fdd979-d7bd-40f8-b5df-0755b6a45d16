"use client";

import React from "react";
import SectionX from "@/components/layout/SectionX";
import { Mail, MapPin, Phone, type LucideIcon } from "lucide-react";
import ContactForm from "@/components/contact/ContactForm";
import Image from "next/image";

type Detail = { sub: string; body: string };
type ContactItem = { icon: LucideIcon; heading: string; details: Detail[] };

const contactItems: ContactItem[] = [
  {
    icon: Mail,
    heading: "Email",
    details: [{ sub: "for inquiries", body: "<EMAIL>" }],
  },
  {
    icon: MapPin,
    heading: "Address",
    details: [
      {
        sub: "Weifield Group Contracting",
        body: "6950 S. Jordan Road – Centennial, CO 80112",
      },
    ],
  },
  {
    icon: Phone,
    heading: "Phone number",
    details: [
      { sub: "Tennessee Office", body: "1.877.WEIFIELD phone" },
      { sub: "Colorado Office", body: "1.877.WEIFIELD phone" },
    ],
  },
];

function ContactCard({ item }: { item: ContactItem }) {
  const { icon: Icon, heading, details } = item;
  return (
    <div className="flex flex-row gap-5 bg-[#E9F2FF] px-5 py-5 ">
      <div className="flex h-16 w-16 items-center justify-center rounded-full bg-[#56687F73]/40 ">
        <Icon
          size={28}
          strokeWidth={2}
          className="text-[#0D1B2A]"
        />
      </div>

      <div className="flex flex-col gap-2 items-start">
        <h3 className="font-plus-jakarta-sans text-xl font-medium text-[#0D1B2A]">
          {heading}
        </h3>

        {details.map((d, i) => (
          <div
            key={i}
            className="space-y-0.5  text-left"
          >
            <p className="font-plus-jakarta-sans text-base font-bold text-[#0D1B2A]">
              {d.sub}
            </p>
            <p className="font-plus-jakarta-sans text-base text-[#0D1B2A]">
              {d.body}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

export default function ContactUs() {
  return (
    <SectionX addClassName="overflow-hidden !px-0">
      <div className="relative flex flex-row py-24 justify-around items-center flex-wrap">
        <div>
          <h2 className="font-plus-jakarta-sans text-5xl text-[#003164] font-bold">
            Contact Us
          </h2>
          <p className="mt-4 max-w-md font-plus-jakarta-sans text-[#1B263B] text-lg">
            We’re here to help. Reach out via any of the methods below and
            someone from our team will get back to you within 24 hours.
          </p>
        </div>
        <div className="rounded-full bg-[#FFD267] h-[243px] w-[243px] hidden xl:block"></div>
        <div className="xl:absolute right-[17%] top-[35%]">
          <ContactForm />
        </div>
      </div>

      <div className="bg-[#091B33] rounded-xl hidden xl:block">
        <div className="flex justify-between items-center">
          <Image
            src={"/yellow_vector.png"}
            alt="Yellow Vector"
            width={500}
            height={500}
            className="max-w-[40dvw] pl-10 pt-10"
          />
          <Image
            src={"/green_ellipse.png"}
            alt="Green Ellipse"
            width={80}
            height={80}
          />
        </div>
      </div>

      <div className="mx-auto rounded-[32px] px-6 lg:px-20 py-16 relative">
        <h1 className="font-noto-serif text-[#091B33]  text-3xl font-medium xl:text-4xl 2xl:text-5xl">
          We Are Here For You
        </h1>

        <div className="flex flex-row items-center  sm:justify-around mt-10 flex-wrap gap-10">
          <ContactCard item={contactItems[0]} />
          <ContactCard item={contactItems[1]} />
        </div>
        <div className="flex flex-row items-center sm:justify-center mt-10">
          <ContactCard item={contactItems[2]} />
        </div>

        <Image
          src={"/yellow_ellipse.png"}
          alt="Green Ellipse"
          width={80}
          height={80}
          className="absolute left-0 bottom-20"
        />
      </div>
    </SectionX>
  );
}
