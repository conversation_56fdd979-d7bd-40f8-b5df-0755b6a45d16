"use client";

import Image from "next/image";
import SectionX from "@/components/layout/SectionX";
import { SocialIcon } from "react-social-icons";
import { MailIcon } from "lucide-react";

const whyChoose = [
  {
    icon: "/logos/ranking.png",
    title: "Hands-On Learning",
    desc: "Hands-on learning with live sessions, assignments, and real-world AI use cases. Hands-on learning with live sessions, assignments, and real-world AI use cases.",
    bg: "bg-[#CCE0F6]",
  },
  {
    icon: "/logos/3dcube.png",
    title: "Hands-On Learning",
    desc: "Hands-on learning with live sessions, assignments, and real-world AI use cases. Hands-on learning with live sessions, assignments, and real-world AI use cases.",
    bg: "bg-[#CCF6CC]",
  },
  {
    icon: "/logos/share.png",
    title: "Hands-On Learning",
    desc: "Hands-on learning with live sessions, assignments, and real-world AI use cases. Hands-on learning with live sessions, assignments, and real-world AI use cases.",
    bg: "bg-[#FDFCD6]",
  },
];

const team = Array.from({ length: 3 }).map((_, idx) => ({
  id: idx,
  name: "<PERSON>",
  role: "Senior Mathematics Instructor",
  img: "/assets/about/user.png",
  bio: "With 12+ years of teaching experience, Sir Hassan makes complex mathematical concepts simple and relatable. His mission: to make every student fall in love with numbers.",
}));

export default function About() {
  return (
    <SectionX addClassName="overflow-hidden">
      <section className="mx-auto grid max-w-7xl gap-10 px-6 pb-16 pt-10 lg:grid-cols-2 lg:items-center lg:gap-24 relative">
        <div>
          <h1 className="mt-1 font-noto-serif text-3xl sm:text-4xl md:text-5xl font-bold  text-[#CDA84E]">
            <span className="font-noto-serif text-xl sm:text-2xl md:text-3xl font-semibold tracking-wide text-[#091B33]">
              About
            </span>
            Empowering Minds <br />
            Shaping Futures
          </h1>

          <Image
            src="/vectors/lines.png"
            alt=""
            width={150}
            height={240}
            className="absolute -left-0 top-10 hidden lg:block "
          />

          <p className="mt-6 max-w-md font-plus-jakarta-sans capitalize leading-relaxed">
            At Rahnaward Academy, we believe that <b>AI Education</b> should be
            accessible, practical, and empowering. We help you unlock your
            potential through cutting-edge, hands-on learning experiences.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <Image
            src="/assets/about/hero_1.png"
            alt=""
            width={500}
            height={240}
            className="col-span-2 rounded-xl object-cover shadow-lg"
          />
        </div>
      </section>

      <section className="relative overflow-hidden rounded-2xl bg-[#091B33] py-20 text-center text-white">
        <Image
          src="/assets/about/bg_2.png"
          alt=""
          width={160}
          height={220}
          className="absolute -left-0 top-0 opacity-20"
        />
        <Image
          src="/assets/about/bg_1.png"
          alt=""
          width={220}
          height={260}
          className="absolute right-0 bottom-0 opacity-20"
        />
        <h2 className="font-noto-serif text-3xl font-semibold">Our Vision</h2>

        <p className="mx-auto mt-6 max-w-2xl font-plus-jakarta-sans font-medium text-lg">
          To democratize AI education and empower individuals across the world
          to innovate, create, and lead using the power of artificial
          intelligence.
        </p>
      </section>

      <section className="mx-auto max-w-6xl px-6 py-20">
        <h2 className="text-center font-noto-serif text-3xl font-semibold text-[#091B33]">
          Why Choose Rahnaward Academy?
        </h2>

        <div className="mt-12 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {whyChoose.map((c, i) => (
            <div
              key={i}
              className={`${c.bg} rounded-xl p-6 space-y-4 shadow-sm`}
            >
              <Image
                src={c.icon}
                alt=""
                width={38}
                height={38}
              />
              <h3 className="font-plus-jakarta-sans text-lg font-bold text-[#091B33]">
                {c.title}
              </h3>
              <p className="font-plus-jakarta-sans font-medium text-[#091B33]">
                {c.desc}
              </p>
            </div>
          ))}
        </div>
      </section>

      <section className="grid overflow-hidden rounded-xl bg-[#CDA84E] py-16 px-6 lg:grid-cols-2 lg:items-center">
        <div className="space-y-6">
          <h3 className="font-noto-serif text-3xl font-bold text-[#091B33]">
            Our Success Depends On Our Students’ Success
          </h3>
          <p className="font-plus-jakarta-sans max-w-md text-[#091B33] leading-loose capitalize">
            At Rahnaward Academy, we believe that <b>AI education</b> should be
            accessible, practical, and empowering. We help you unlock your
            potential through cutting-edge, hands-on learning experiences.
          </p>

          <div className="mt-8 grid grid-cols-3 gap-5 text-center divide-x divide-[#091B33]/30 max-w-2xl">
            {["Students", "Active Courses", "Teachers"].map((label) => (
              <div
                key={label}
                className="px-4"
              >
                <p className="font-plus-jakarta-sans text-4xl font-bold text-[#091B33]">
                  100+
                </p>
                <p className="font-plus-jakarta-sans text-sm font-medium text-[#55585F]">
                  {label}
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-10 flex justify-center lg:mt-0">
          <Image
            src="/assets/about/hero_2.png"
            alt=""
            width={500}
            height={420}
            className="rounded-xl object-cover shadow-lg"
          />
        </div>
      </section>

      <section className="mx-auto max-w-7xl px-6 py-20">
        <h2 className="text-center font-noto-serif text-3xl font-semibold text-[#091B33]">
          The People Behind Rahnaward Academy
        </h2>
        <p className="mx-auto mt-4 max-w-3xl text-center font-plus-jakarta-sans text-[#415A77]">
          We’re a passionate group of educators, technologists, and AI
          innovators working to make learning smarter, more accessible, and
          future-ready.
        </p>

        <div className="mt-14 grid gap-12 sm:grid-cols-2 lg:grid-cols-3">
          {team.map((m, idx) => (
            <article
              key={m.id}
              className={
                "text-center transition lg:duration-300" +
                (idx === 1 ? " lg:-translate-y-6" : "")
              }
            >
              <Image
                src={m.img}
                alt={m.name}
                width={160}
                height={160}
                className="mx-auto rounded-full object-cover"
              />
              <h3 className="mt-6 font-plus-jakarta-sans text-lg font-semibold text-[#091B33]">
                {m.name}
              </h3>
              <p className="text-sm font-plus-jakarta-sans font-medium text-[#091B33]">
                {m.role}
              </p>
              <p className="font-plus-jakarta-sans mt-3 text-sm text-[#091B33]">
                {m.bio}
              </p>

              <div className="mt-4 flex items-center justify-center gap-4">
                <SocialIcon
                  url="https://www.linkedin.com"
                  bgColor="#091B33"
                  fgColor="#E0E1DD"
                  style={{ height: 24, width: 24 }}
                />
                <MailIcon
                  className="h-6 w-6 cursor-pointer text-[#091B33]"
                  onClick={() =>
                    (window.location.href = "mailto:<EMAIL>")
                  }
                />
              </div>
            </article>
          ))}
        </div>
      </section>
    </SectionX>
  );
}
