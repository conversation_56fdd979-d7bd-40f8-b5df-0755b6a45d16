import React from "react";

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  addClassName?: string;
}

function ThemedButton({ addClassName, ...props }: Props) {
  return (
    <button
      type="button"
      className={`font-plus-jakarta-sans right-1.5 top-1.5 rounded-full bg-[#1B263B] px-6 py-2 text-xs text-white transition-all hover:bg-theme_primary/90 cursor-pointer font-medium ${addClassName}`}
      {...props}
    >
      {props.children}
    </button>
  );
}

export default ThemedButton;
