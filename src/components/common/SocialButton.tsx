import Image from "next/image";
import React from "react";

type Props = {
  provider: string;
  iconSrc: string;
};

function SocialButton({ provider, iconSrc }: Props) {
  return (
    <button
      type="button"
      className="font-inter flex w-full items-center px-4 gap-3 rounded-md border border-neutral-300 bg-white py-3 text-sm font-semibold transition hover:bg-neutral-50 text-[#1B263B] hover:border-theme_primary"
    >
      <Image
        src={iconSrc}
        alt=""
        width={24}
        height={24}
      />
      <span className="self-center w-full">Continue with {provider}</span>
    </button>
  );
}

export default SocialButton;
