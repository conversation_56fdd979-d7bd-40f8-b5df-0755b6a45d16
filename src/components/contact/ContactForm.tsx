import React from "react";

function Input({
  type = "text",
  ...rest
}: React.InputHTMLAttributes<HTMLInputElement>) {
  return (
    <input
      type={type}
      className="font-plus-jakarta-sans w-full rounded-md border border-neutral-300 bg-white p-3 text-sm outline-none focus:border-theme_primary text-[#091B33]"
      {...rest}
    />
  );
}

function ContactForm() {
  return (
    <form
      onSubmit={(e) => e.preventDefault()}
      className="flex flex-col gap-6 shadow-lg p-10 bg-white rounded-md"
    >
      <h1 className="font-plus-jakarta-sans text-[#415A77] font-medium text-3xl">
        Have a Question? Let&apos;s Talk!{" "}
      </h1>
      <Input
        placeholder="Your Name"
        required
      />
      <Input
        type="email"
        placeholder="Email address*"
        required
      />
      <textarea
        placeholder="Write your message here…"
        className="font-plus-jakarta-sans h-40 w-full rounded-md border border-neutral-300 bg-white p-4 text-sm outline-none focus:border-theme_primary"
      />
      <button
        type="submit"
        className="font-plus-jakarta-sans rounded-md bg-[#CDA84E] py-3 font-medium text-black transition hover:bg-[#CDA84E]/90"
      >
        Send
      </button>
    </form>
  );
}

export default ContactForm;
