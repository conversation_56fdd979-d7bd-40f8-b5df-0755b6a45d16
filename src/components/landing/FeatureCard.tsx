import Image from "next/image";
import Link from "next/link";
import React from "react";

type Props = {
  title: string;
  description: string;
  imageUrl?: string;
  link?: string;
};

function FeatureCard({ title, description, imageUrl, link }: Props) {
  return (
    <Link href={link ?? "/"}>
      <div className="flex flex-col max-w-[416px] gap-y-2">
        <div className="h-[500px] sm:w-[416px] w-full rounded-[24px] bg-[#091B33] flex items-center justify-center">
          {imageUrl && (
            <Image
              src={imageUrl}
              alt={title}
              width={300}
              height={350}
            />
          )}
        </div>
        <span className="font-plus-jakarta-sans text-2xl  font-semibold text-[#1B263B]">
          {title}
        </span>
        <span className="font-plus-jakarta-sans text-base text-[#415A77]">
          {description}
        </span>
      </div>
    </Link>
  );
}

export default FeatureCard;
