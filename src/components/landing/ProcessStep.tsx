import React from "react";

type Props = {
  id: number;
  title: string;
  description: string;
};

function ProcessStep({ id, title, description }: Props) {
  return (
    <div className="flex items-start gap-6">
      <div className="font-plus-jakarta-sans bg-[#091B33] text-white flex h-10 w-10 shrink-0 items-center justify-center rounded-full text-2xl font-semibold">
        {id}
      </div>

      <div>
        <h3 className="font-plus-jakarta-sans mb-5 text-3xl font-semibold">
          {title}
        </h3>
        <p className="font-plus-jakarta-sans text-lg leading-relaxed text-[#1B263B]">
          {description}
        </p>
      </div>
    </div>
  );
}

export default ProcessStep;
