import Image from "next/image";
import React from "react";

type Props = {
  text: string;
  user: {
    name: string;
    designation: string;
    avatar: string;
  };
};

function TestimonialCard({ text, user }: Props) {
  return (
    <div className="flex h-full flex-col gap-6 rounded-xl bg-neutral-100 p-6 shadow-lg hover:scale-105 transiation-all duration-300 ease-in-out">
      <div className=" font-bold text-7xl text-[#091B33]">“</div>

      <p className="font-plus-jakarta-sans flex-1 text-[#1B263B]">{text}</p>

      <div className="flex items-center gap-3">
        <Image
          unoptimized
          src={user.avatar ?? "https://placehold.co/32"}
          alt={`${user.name} avatar`}
          width={32}
          height={32}
          className="h-8 w-8 rounded-full object-cover"
        />
        <div>
          <div className="font-plus-jakarta-sans text-[#1B263B]">
            {user.name}
          </div>
          <div className="font-plus-jakarta-sans text-xs text-[#415A77]">
            {user.designation}
          </div>
        </div>
      </div>
    </div>
  );
}

export default TestimonialCard;
