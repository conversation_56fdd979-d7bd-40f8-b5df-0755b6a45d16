import React from "react";
import ThemedButton from "../common/ThemedButton";

function LandingSearch() {
  return (
    <div className="mt-5 flex justify-center">
      <div className="relative flex w-full max-w-2xl">
        <span className="pointer-events-none absolute left-4 top-1/2 -translate-y-1/2 text-neutral-500">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
            className="h-5 w-5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M21 21l-4.35-4.35m1.42-5.67a7.75 7.75 0 11-15.5 0 7.75 7.75 0 0115.5 0z"
            />
          </svg>
        </span>
        <input
          type="text"
          placeholder="What do you want to learn today?"
          className="font-plus-jakarta-sans flex-grow rounded-full bg-white/90 py-3 pl-12 pr-40 lg:text-sm text-xs  text-neutral-900 placeholder-neutral-500 focus:outline-none w-full"
        />
        <ThemedButton addClassName="absolute">Explore</ThemedButton>
      </div>
    </div>
  );
}

export default LandingSearch;
