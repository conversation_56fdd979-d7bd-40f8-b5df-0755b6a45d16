import React from "react";
import TestimonialCard from "../TestimonialCard";

type Testimonial = {
  id: number;
  text: string;
  user: {
    name: string;
    designation: string;
    avatar: string;
  };
};

const testimonials: Testimonial[] = [
  {
    id: 1,
    text: "Rahnaward Academy completely changed how I learn — the courses are practical, clear, and actually fun to follow! I used to feel lost about how to use AI, but now I’ve built my own project thanks to what I learned here. The step-by-step approach made it super easy to stay on track, even with my busy school schedule.",
    user: {
      name: "<PERSON><PERSON>",
      designation: "High School Student",
      avatar: "https://placehold.co/32",
    },
  },
  {
    id: 2,
    text: "Rahnaward Academy completely changed how I learn — the courses are practical, clear, and actually fun to follow! I used to feel lost about how to use AI, but now I’ve built my own project thanks to what I learned here. The step-by-step approach made it super easy to stay on track, even with my busy school schedule.",
    user: {
      name: "<PERSON><PERSON>",
      designation: "High School Student",
      avatar: "https://placehold.co/32",
    },
  },
  {
    id: 3,
    text: "Rahnaward Academy completely changed how I learn — the courses are practical, clear, and actually fun to follow! I used to feel lost about how to use AI, but now I’ve built my own project thanks to what I learned here. The step-by-step approach made it super easy to stay on track, even with my busy school schedule.",
    user: {
      name: "<PERSON>ina <PERSON>",
      designation: "High School Student",
      avatar: "https://placehold.co/32",
    },
  },
  {
    id: 4,
    text: "Rahnaward Academy completely changed how I learn — the courses are practical, clear, and actually fun to follow! I used to feel lost about how to use AI, but now I’ve built my own project thanks to what I learned here. The step-by-step approach made it super easy to stay on track, even with my busy school schedule.",
    user: {
      name: "Amina Khalid",
      designation: "High School Student",
      avatar: "https://placehold.co/32",
    },
  },
  {
    id: 5,
    text: "Rahnaward Academy completely changed how I learn — the courses are practical, clear, and actually fun to follow! I used to feel lost about how to use AI, but now I’ve built my own project thanks to what I learned here. The step-by-step approach made it super easy to stay on track, even with my busy school schedule.",
    user: {
      name: "Amina Khalid",
      designation: "High School Student",
      avatar: "https://placehold.co/32",
    },
  },
  {
    id: 6,
    text: "Rahnaward Academy completely changed how I learn — the courses are practical, clear, and actually fun to follow! I used to feel lost about how to use AI, but now I’ve built my own project thanks to what I learned here. The step-by-step approach made it super easy to stay on track, even with my busy school schedule.",
    user: {
      name: "Amina Khalid",
      designation: "High School Student",
      avatar: "https://placehold.co/32",
    },
  },
];

export default function TestimonialsSection() {
  return (
    <section className="bg-white">
      <div className="mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8">
        <h2 className="font-plus-jakarta-sans text-center text-4xl font-semibold sm:text-5xl">
          Testimonials
        </h2>
        <p className="font-plus-jakarta-sans text-2xl mx-auto mt-4 max-w-2xl text-center text-[#415A77]">
          Don’t take our word for it! Hear it from our students.
        </p>

        <div className="mt-14 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((t) => (
            <TestimonialCard
              key={t.id}
              text={t.text}
              user={t.user}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
