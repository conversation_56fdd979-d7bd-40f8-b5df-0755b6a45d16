import Image from "next/image";
import React from "react";
import ProcessStep from "../ProcessStep";
import SectionX from "@/components/layout/SectionX";

export default function ProcessSection() {
  return (
    <section className="bg-white pb-16">
      <div className="flex items-center justify-center pt-24 pb-16">
        <h2 className="font-plus-jakarta-sans text-center sm:text-5xl text-4xl font-semibold">
          How Rahnaward Academy Works
        </h2>
      </div>

      <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2 px-5">
        <SectionX addClassName="space-y-10">
          <ProcessStep
            id={1}
            title="Sign Up"
            description="Create your free account in seconds to unlock access to personalised learning and AI-powered support."
          />
          <ProcessStep
            id={2}
            title="Select Your Course"
            description="Browse curated courses based on your goals — from mastering AI tools to launching your own business."
          />
          <ProcessStep
            id={3}
            title="Select Payment Method"
            description="Choose a secure and convenient way to pay — we support multiple options for a smooth checkout."
          />
        </SectionX>

        <div className="flex overflow-hidden not-lg:hidden">
          <div className="overflow-hidden  rounded-2xl shadow-lg h-[947px] w-full relative">
            <Image
              className="object-cover object-left"
              src="/assets/home/<USER>"
              fill
              alt="Rahnaward Academy sign-up preview"
              priority
            />
          </div>
        </div>
      </div>
    </section>
  );
}
