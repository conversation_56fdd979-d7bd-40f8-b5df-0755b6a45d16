import SectionX from "@/components/layout/SectionX";
import AchievementCard from "../AchievementCard";

export default function AchievementsSection() {
  return (
    <section>
      <SectionX addClassName="py-20">
        <h1 className="font-plus-jakarta-sans 2xl:text-5xl xl:text-4xl font-semibold  text-3xl text-[#1B263B]">
          What we’ve achieved
        </h1>

        <div
          className="
            mt-10 grid gap-5
            grid-cols-1                          /* mobile 1-col  */
            sm:grid-cols-2                       /* tablet 2-col */
            lg:grid-cols-3                       /* desktop 3-col*/
            auto-rows-[1fr]                     /* equal height */
          "
        >
          <AchievementCard
            topText="Students empowered worldwide"
            middleText="500K+"
            bottomText="And growing everyday"
          />
          <AchievementCard
            topText="Highly engaging content delivered"
            middleText="1000K+"
            bottomText="From bite-sized courses to in-depth lessons"
          />
          <AchievementCard
            topText="Students satisfaction ratings"
            middleText="4.8/5"
            bottomText="Committed to the best learning experience"
          />

          <AchievementCard
            className="sm:col-span-2 lg:col-span-2"
            topText="In-demand skills taught"
            middleText="200+"
            bottomText="Equip yourself for the future of work"
          />
          <AchievementCard
            topText="Practical hands-on learning"
            middleText="100%"
            bottomText="Apply your knowledge to real-world projects"
          />
        </div>
      </SectionX>
    </section>
  );
}
