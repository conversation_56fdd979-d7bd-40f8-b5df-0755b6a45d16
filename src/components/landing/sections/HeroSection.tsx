/* ───────────────────────── HeroSection.tsx ───────────────────────── */
"use client";

import Image from "next/image";
import LandingSearch from "../LandingSearch";
import SectionX from "@/components/layout/SectionX";

export default function HeroSection() {
  return (
    <SectionX addClassName="h-[calc(100vh-90px)] overflow-hidden max-sm:p-0">
      <div className="relative mx-auto flex h-full flex-col overflow-hidden rounded-[32px] max-sm:rounded-none bg-theme_primary pt-8 sm:pt-10 md:pt-12 text-white">
        <Image
          src="/vectors/arrow_vector.png"
          alt=""
          width={72}
          height={72}
          className="absolute left-[8%] top-4 hidden lg:block xl:left-[12%]"
        />
        <Image
          src="/vectors/star_2.png"
          alt=""
          width={32}
          height={32}
          className="absolute left-[14%] top-1/2 hidden -translate-y-1/2 lg:block"
        />
        <Image
          src="/vectors/star_1.png"
          alt=""
          width={32}
          height={32}
          className="absolute right-[12%] top-[22%] hidden lg:block"
        />

        <div className="mx-auto w-full max-w-3xl px-4 text-center sm:px-6">
          <h1 className="font-noto-serif text-2xl sm:text-3xl lg:text-4xl 2xl:text-5xl">
            Best Courses Are Waiting To&nbsp;
            <br className="hidden md:block" />
            Enrich Your Skills
          </h1>
          <p className="mx-auto mt-5 max-sm:mt-10  max-w-md text-xs sm:max-w-lg sm:text-sm  2xl:text-lg font-plus-jakarta-sans text-[#E0E1DD]">
            Provides you with the latest online learning system and material
            that help your knowledge growing.
          </p>

          <div className="mt-6 md:mt-8  max-sm:mt-20">
            <LandingSearch />
          </div>
        </div>

        <div className="mt-auto flex justify-center px-4 ">
          <Image
            src="/assets/home/<USER>"
            alt="Dashboard Placeholder"
            width={1600}
            height={900}
            priority
            className="max-w-[calc(100dvw-400px)] rounded-[12px] shadow-sm max-lg:max-w-[100dvw]"
          />
        </div>
      </div>
    </SectionX>
  );
}
