type Props = {
  topText: string;
  middleText: string;
  bottomText: string;
  className?: string;
};

export default function AchievementCard({
  topText,
  middleText,
  bottomText,
  className = "",
}: Props) {
  return (
    <div
      className={`
          rounded-2xl bg-[#E0E1DD66]/40 p-6 shadow-lg hover:scale-105 transition-all duration-300 ease-in-out
          flex flex-col justify-between
          ${className}
        `}
    >
      <h3 className="font-plus-jakarta-sans font-semibold text-[#1B263B]">
        {topText}
      </h3>
      <p className="font-plus-jakarta-sans mt-4 xl:text-4xl  text-4xl 3xl:text-6xl  font-semibold text-[#091B33]">
        {middleText}
      </p>
      <p className="font-plus-jakarta-sans mt-2 text-[#1B263B] font-medium">
        {bottomText}
      </p>
    </div>
  );
}
