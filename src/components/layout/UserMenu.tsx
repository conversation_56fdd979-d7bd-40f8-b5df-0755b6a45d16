"use client";

import { useState } from "react";
import { ChevronDown, LogOut, User } from "lucide-react";
import { useAuth } from "@/providers/AuthProvider";
import { logout } from "@/services/auth";
import { ROUTES } from "@/constants/routes.constants";

export default function UserMenu() {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);

  if (!user) return null;

  return (
    <div className="font-plus-jakarta-sans relative">
      <button
        onClick={() => setOpen((v) => !v)}
        className="flex items-center gap-2 rounded-md px-3 py-1.5 hover:bg-neutral-100 cursor-pointer"
      >
        <User size={16} />
        <span className="hidden sm:inline-block font-inter text-sm">
          {user.first_name ?? user.email}
        </span>
        <ChevronDown size={16} />
      </button>

      {open && (
        <div
          className="absolute right-0 mt-2 w-56 rounded-md border bg-white shadow-lg"
          onMouseLeave={() => setOpen(false)}
        >
          <div className="px-4 py-3">
            <p className="font-semibold text-sm">{user.first_name ?? "—"}</p>
            <p className="text-xs text-neutral-600 truncate">{user.email}</p>
          </div>
          <button
            className="flex w-full items-center gap-2 px-4 py-2 text-sm bg-theme_primary/20 text-black cursor-pointer hover:bg-theme_primary/80 hover:text-white"
            onClick={async () => {
              await logout();
              window.location.href = ROUTES.SIGN_IN;
            }}
          >
            <LogOut size={16} /> Sign out
          </button>
        </div>
      )}
    </div>
  );
}
