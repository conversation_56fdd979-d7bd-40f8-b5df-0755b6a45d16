"use client";

import Link from "next/link";
import Logo from "../base/Logo";
import Title from "../base/Title";
import DesktopNavBar from "./DesktopNavBar";
import AuthButtons from "./AuthButtons";
import UserMenu from "./UserMenu";
import { useAuth } from "@/providers/AuthProvider";

function Header() {
  const { user } = useAuth();

  return (
    <header className="sticky top-0 z-50 w-full  bg-white h-header">
      <nav className="mx-auto flex max-w-7xl items-center justify-between px-4 py-4 md:px-6 lg:px-8">
        <Link
          href="/"
          className="flex items-center gap-2 whitespace-nowrap text-neutral-900"
        >
          <Logo />
          <Title />
        </Link>

        <DesktopNavBar />

        {user ? <UserMenu /> : <AuthButtons />}
      </nav>
    </header>
  );
}

export default Header;
