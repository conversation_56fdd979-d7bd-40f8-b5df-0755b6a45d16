"use client";

import Image from "next/image";
import Link from "next/link";
import { SocialIcon } from "react-social-icons";

export default function Footer() {
  return (
    <footer className="bg-theme_primary text-white">
      <div className="mx-auto max-w-7xl px-4 pt-16 pb-10 sm:px-6 lg:px-8">
        <div className="grid gap-12 md:grid-cols-5">
          <div className="md:col-span-2">
            <div className="flex items-center gap-2">
              <Image
                src="/logo.jpg"
                alt="Rahnaward logo"
                width={28}
                height={28}
              />
              <span className="font-poppins font-bold text-3xl">
                Rahnaward Academy
              </span>
            </div>

            <p className="mt-4  font-inter">
              Our experts can provide valuable courses and assist you in
              identifying real world problems.
            </p>

            <div className="mt-6 flex gap-4">
              <SocialIcon
                url="https://www.instagram.com/"
                fgColor="#000"
                bgColor="#FFFFFF"
                style={{ height: 32, width: 32 }}
              />
              <SocialIcon
                url="https://www.facebook.com/"
                fgColor="#000"
                bgColor="#FFFFFF"
                style={{ height: 32, width: 32 }}
              />
              <SocialIcon
                url="https://www.youtube.com/"
                fgColor="#000"
                bgColor="#FFFFFF"
                style={{ height: 32, width: 32 }}
              />
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="font-inter mb-2 font-medium text-lg">About Us</h4>
            <ul className="space-y-2 font-inter">
              <li>
                <Link href="/">About Academy</Link>
              </li>
              <li>
                <Link href="/">Our Mission &amp; Vision</Link>
              </li>
              <li>
                <Link href="/">Careers</Link>
              </li>
            </ul>
          </div>

          <div className="space-y-4">
            <h4 className="font-inter mb-2 font-medium text-lg">Courses</h4>
            <ul className="space-y-2 font-inter">
              <li>
                <Link href="#">All Courses</Link>
              </li>
              <li>
                <Link href="#">Signature Programs</Link>
              </li>
              <li>
                <Link href="#">Short Courses</Link>
              </li>
              <li>
                <Link href="#">Skill Paths</Link>
              </li>
              <li>
                <Link href="#">Certifications</Link>
              </li>
            </ul>
          </div>

          <div className="space-y-4">
            <h4 className="font-inter mb-2 font-medium text-lg">Support</h4>
            <ul className="space-y-2 font-inter">
              <li>
                <Link href="#">Help Center</Link>
              </li>
              <li>
                <Link href="#">Contact Us</Link>
              </li>
              <li>
                <Link href="#">FAQs</Link>
              </li>
              <li>
                <Link href="#">Help Line</Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 border-t border-white/20 pt-6">
          <div className="font-inter flex flex-col items-center justify-between gap-4 sm:flex-row">
            <p>©2025 Rahnaward Academy. All rights reserved.</p>
            <Link href="#">Term &amp; Condition</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
